import * as React from "react";
import ScreenWrapper from "../../../components/ScreenWrapper";
import {
  Text,
  View,
  TouchableOpacity,
  Alert,
  Platform,
  PermissionsAndroid,
} from "react-native";
import {
  usecaseSynopsisDetails,
  useLoaderAndError,
} from "../hooks/reportHooks";
import { fetchcaseSynopsis } from "../../../store/rep/ScheduleStack/report/thunk";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { useCoordinatorSelectedPatient } from "../hooks/schedulesHooks";
import { useFocusEffect } from "@react-navigation/native";
import { capitalizeName, formatReportData } from "../../../utils";
import Loader from "../../../components/Loader";
import moment from "moment";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import ViewShot from "react-native-view-shot";

interface IReportScreenProps {}

const ReportScreen: React.FunctionComponent<IReportScreenProps> = (props) => {
  const dispatch = useDispatch<AppDispatch>();
  const selectedPatient = useCoordinatorSelectedPatient();
  const details = usecaseSynopsisDetails();
  const reportDetails = formatReportData(details.caseSynopsis);

  const { loader, error } = useLoaderAndError();
  const viewShotRef = React.useRef<any>(null);

  // Helper function to find the largest W value in baseline measurements
  const findLargestW = (baselineTable: any) => {
    if (!baselineTable) return null;
    let largestW = 0;
    let largestWKey = null;

    Object.entries(baselineTable).forEach(([key, values]: [string, any]) => {
      if (!isNaN(Number(key)) && values?.value1) {
        const wValue = parseFloat(values.value1);
        if (wValue > largestW) {
          largestW = wValue;
          largestWKey = key;
        }
      }
    });

    return largestWKey;
  };

  // Helper function to find the lowest compression percentage in final measurements
  const findLowestCompression = (finalMeasurements: any) => {
    if (!finalMeasurements) return null;
    let lowestCompression = Infinity;
    let lowestCompressionKey = null;

    Object.entries(finalMeasurements).forEach(
      ([key, values]: [string, any]) => {
        if (!isNaN(Number(key)) && values?.per) {
          const compressionValue = parseFloat(values.per);
          if (compressionValue < lowestCompression) {
            lowestCompression = compressionValue;
            lowestCompressionKey = key;
          }
        }
      }
    );

    return lowestCompressionKey;
  };

  const largestWKey = findLargestW(reportDetails?.baselineTable);
  const lowestCompressionKey = findLowestCompression(
    reportDetails?.finalMeasurements
  );

  // Screenshot functionality
  const handleScreenshot = async () => {
    try {
      if (viewShotRef.current) {
        const uri = await viewShotRef.current.capture();
        console.log("Screenshot saved to:", uri);
        // You can add additional logic here to save or share the screenshot
      }
    } catch (error) {
      console.error("Screenshot failed:", error);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          const response = await dispatch(
            fetchcaseSynopsis({
              case_id: selectedPatient.case_id,
            })
          );
        }
      };

      fetchDetails();
    }, [selectedPatient?.case_id, dispatch])
  );

  const elementSection2 = reportDetails?.baselineTable;
  const table2 = reportDetails?.finalMeasurements;

  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;
    await dispatch(
      fetchcaseSynopsis({
        case_id: selectedPatient.case_id,
      })
    );
  };

  if (loader) {
    return <Loader />;
  }

  return (
    <ViewShot ref={viewShotRef} options={{ format: "png", quality: 0.8 }}>
      <ScreenWrapper direction="column" onRefresh={handleRefresh}>
        {/* Header with Screenshot Button */}
        <View className="flex-row justify-end items-center pb-2 mb-2">
          <TouchableOpacity
            onPress={handleScreenshot}
            className="bg-primaryPurple p-2 rounded-lg"
          >
            <MaterialCommunityIcons name="camera" color="#FFFFFF" size={20} />
          </TouchableOpacity>
        </View>

        {/* Patient Information Section */}
        <View className=" mb-2">
          <View className="flex-row justify-between mb-2">
            <View className="flex-1">
              <Text className="font-semibold text-primaryBlack">
                PT NAME:{" "}
                {reportDetails.ptName
                  ? capitalizeName(reportDetails.ptName)
                  : "N/A"}
              </Text>
            </View>
            <View className="flex-1">
              <Text className="font-semibold text-primaryBlack text-right">
                DOB:{" "}
                {reportDetails?.dob
                  ? moment(reportDetails.dob).format("MM/DD/YYYY")
                  : "N/A"}
              </Text>
            </View>
          </View>

          <Text className="font-semibold text-primaryBlack mb-2">
            CHA2DS2-VASC: {reportDetails?.CHAD || "N/A"}
          </Text>
          <Text className="font-semibold text-primaryBlack mb-2">
            PT RATIONALE: {reportDetails?.ptRationale || "N/A"}
          </Text>
          <Text className="font-semibold text-primaryBlack mb-2">
            PROCEDURE DATE:{" "}
            {reportDetails?.date
              ? moment(reportDetails.date).format("MM/DD/YYYY")
              : "N/A"}
          </Text>
          <Text className="font-semibold text-primaryBlack mb-2">
            SITE: {reportDetails?.hospital || "N/A"}
          </Text>
          <Text className="font-semibold text-primaryBlack mb-2">
            IMPLANTING PROVIDER: {reportDetails?.implantingMD || "N/A"}
          </Text>
          <Text className="font-semibold text-primaryBlack mb-2">
            CASE SPECIALIST: {reportDetails?.caseSpecialist || "N/A"}
          </Text>
          <Text className="font-semibold text-primaryBlack mb-2">
            REFERRING PROVIDER: {reportDetails?.referringProvider || "N/A"}
          </Text>
          <Text className="font-semibold text-primaryBlack">
            PCP: {reportDetails?.pcp || "N/A"}
          </Text>
        </View>

        {/* Device Section */}
        <View className=" mb-2">
          <Text className="font-semibold text-primaryBlack">
            DEVICE:{" "}
            {reportDetails?.device
              ? `${reportDetails.device} ${
                  reportDetails.deviceName || ""
                }`.trim()
              : "N/A"}
          </Text>
        </View>

        {/* Tables Section */}
        <View className="flex-row justify-between mb-2">
          {/* Baseline Measurements Table */}
          {elementSection2 && (
            <View className="flex-1 mr-2">
              <View className="border border-primaryBlack rounded-lg p-4 bg-white">
                <Text className="font-semibold text-center text-primaryBlack mb-2">
                  {elementSection2?.tableHeading?.toUpperCase() ||
                    "BASELINE MEASUREMENTS"}
                </Text>
                <View className="flex-row border-b border-primaryBlack py-2">
                  <Text className="flex-1 font-semibold text-center text-primaryPurple mt-3">
                    Angle
                  </Text>
                  <Text className="flex-1 font-semibold text-center text-primaryPurple">
                    W {"       "}(mm)
                  </Text>
                  <Text className="flex-1 font-semibold text-center text-primaryPurple">
                    L {"    "} (mm)
                  </Text>
                </View>

                {Object.entries(elementSection2).map(
                  ([key, values]: [string, any], index) => {
                    if (isNaN(Number(key))) return null;
                    const isLargestW = key === largestWKey;

                    const filteredLength = Object.entries(
                      elementSection2
                    ).filter(([k]) => !isNaN(Number(k))).length;

                    return (
                      <View
                        key={index}
                        className={`flex-row py-2 ${
                          index === filteredLength - 1
                            ? "border-none"
                            : "border-b border-primaryBlack"
                        }`}
                      >
                        <Text className="flex-1 text-center text-primaryBlack">
                          {key}°
                        </Text>

                        <View
                          className={`flex-1 items-center justify-center ${
                            isLargestW
                              ? "border-2 border-green-3 rounded-full"
                              : ""
                          }`}
                        >
                          <Text
                            className={`text-center ${
                              isLargestW
                                ? "font-bold text-primaryBlack"
                                : "text-primaryBlack"
                            }`}
                          >
                            {values?.value1 || "N/A"}
                          </Text>
                        </View>

                        <Text className="flex-1 text-center text-primaryBlack">
                          {values?.value2 || "N/A"}
                        </Text>
                      </View>
                    );
                  }
                )}
              </View>
            </View>
          )}

          {/* Final Measurements Table */}
          {table2 && (
            <View className="flex-1 ml-2">
              <View className="border border-primaryBlack rounded-lg p-4 bg-primaryWhite">
                <Text className="font-semibold text-center text-primaryBlack mb-2">
                  {table2?.heading?.toUpperCase() || "FINAL MEASUREMENTS"}
                </Text>

                <View className="flex-row border-b border-primaryBlack py-2">
                  <Text className="flex-1 font-semibold text-center text-primaryPurple mt-3">
                    Angle
                  </Text>
                  <Text className="flex-1 font-semibold text-center text-primaryPurple">
                    W {"      "}(mm)
                  </Text>
                  <Text className="flex-1 font-semibold text-center text-primaryPurple">
                    C {"      "}(%)
                  </Text>
                </View>

                {Object.entries(table2)?.map(
                  ([key, values]: [string, any], index) => {
                    if (isNaN(Number(key))) return null;
                    const isLowestCompression = key === lowestCompressionKey;

                    const filteredLength = Object.entries(table2).filter(
                      ([k]) => !isNaN(Number(k))
                    ).length;

                    return (
                      <View
                        key={index}
                        className={`flex-row py-2 ${
                          index === filteredLength - 1
                            ? "border-none"
                            : "border-b border-primaryBlack"
                        }`}
                      >
                        <Text className="flex-1 text-center text-primaryBlack">
                          {key}°
                        </Text>
                        <Text className="flex-1 text-center text-primaryBlack">
                          {values?.w || "N/A"}
                        </Text>

                        <View
                          className={`flex-1 items-center justify-center ${
                            isLowestCompression
                              ? "border-2 border-green-3 rounded-full"
                              : ""
                          }`}
                        >
                          <Text
                            className={`text-center ${
                              isLowestCompression
                                ? "font-bold text-primaryBlack"
                                : "text-primaryBlack"
                            }`}
                          >
                            {values?.per || "N/A"}
                          </Text>
                        </View>
                      </View>
                    );
                  }
                )}
              </View>
            </View>
          )}
        </View>

        {/* Additional Information Section */}
        <View className=" mb-2">
          <Text className="font-semibold text-primaryBlack mb-2">
            LAA TYPE: {reportDetails?.laaType || "N/A"}
          </Text>
          <Text className="font-semibold text-primaryBlack mb-2">
            FINAL TSP LOCATION: {reportDetails?.finalTspLocation || "N/A"}
          </Text>
          <Text className="font-semibold text-primaryBlack mb-2">
            COMPLICATIONS: {reportDetails?.complications || "N/A"}
          </Text>
          <Text className="font-semibold text-primaryBlack mb-2">
            LEAK: {reportDetails?.leak ? `${reportDetails.leak} mm` : "None"}
          </Text>
          <View className="flex-row justify-between mb-2">
            <Text className="font-semibold text-primaryBlack flex-1">
              LAP: {reportDetails?.lap ? `${reportDetails.lap} mmHg` : "N/A"}
            </Text>
            <Text className="font-semibold text-primaryBlack flex-1">
              ACT: {reportDetails?.act ? `${reportDetails.act} sec` : "N/A"}
            </Text>
          </View>
          <Text className="font-semibold text-primaryBlack mb-2">
            FLUORO TIME:{" "}
            {reportDetails?.fluoroTime
              ? `${reportDetails.fluoroTime} min`
              : "N/A"}
          </Text>
          <Text className="font-semibold text-primaryBlack">
            POST DRUG RX:{" "}
            {reportDetails?.postDrugRx
              ? reportDetails.postDrugRx
                  .map((item: string) =>
                    item.includes("Indefinitely")
                      ? item.replace("x 0", "-").trim()
                      : item
                  )
                  .join(", ")
              : "N/A"}
          </Text>
        </View>
      </ScreenWrapper>
    </ViewShot>
  );
};

export default ReportScreen;
